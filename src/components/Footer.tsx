'use client';

import Link from 'next/link';
import { t, type Locale } from '@/lib/i18n';

interface FooterProps {
  locale: Locale;
}

const Footer = ({ locale }: FooterProps) => {
  const currentYear = new Date().getFullYear();
  
  return (
    // 修改 - 页脚采用更克制的苹果风格底色与发丝分隔线，移除强烈渐变 [Apple HIG - Confirmed via mcp-feedback-enhanced]
<footer className="mt-auto bg-neutral-50/90 supports-[backdrop-filter]:bg-neutral-50/80 backdrop-blur border-t border-neutral-200/70">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-10">
        <div className="md:flex md:justify-between">
          <div className="mb-8 md:mb-0">
            <Link href={`/${locale}`} className="text-xl font-bold text-primary">
              {/* 修改 - 移除品牌渐变标题，采用系统中性色与更克制的权重 [Apple HIG] */}
                  <span className="text-xl md:text-2xl font-semibold tracking-[-0.01em] text-neutral-900 hover:text-neutral-700 transition-colors">
                {locale === 'zh' ? '三娃软件开发工作室' : 'Sanva Studio'}
              </span>
            </Link>
            <p className="mt-2 text-sm text-neutral-500">
              {t('footer.tagline', locale)}
            </p>
          </div>
          <div className="grid grid-cols-2 gap-8 sm:grid-cols-3">
            <div>
              <h3 className="text-sm font-medium text-neutral-900">
                {t('footer.navigation', locale)}
              </h3>
              <ul className="mt-4 space-y-2">
                <li>
                  <Link href={`/${locale}`} className="text-sm text-neutral-600 hover:text-neutral-900">
                    {t('navigation.home', locale)}
                  </Link>
                </li>
                <li>
                  <Link href={`/${locale}/about`} className="text-sm text-neutral-600 hover:text-neutral-900">
                    {t('navigation.about', locale)}
                  </Link>
                </li>
                <li>
                  <Link href={`/${locale}/services`} className="text-sm text-neutral-600 hover:text-neutral-900">
                    {t('navigation.services', locale)}
                  </Link>
                </li>
                <li>
                  <Link href={`/${locale}/contact`} className="text-sm text-neutral-600 hover:text-neutral-900">
                    {t('navigation.contact', locale)}
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-sm font-medium text-neutral-900">
                {t('footer.services', locale)}
              </h3>
              <ul className="mt-4 space-y-2">
                <li>
                  <Link href={`/${locale}/services`} className="text-sm text-neutral-600 hover:text-neutral-900">
                    {t('footer.appDevelopment', locale)}
                  </Link>
                </li>
                <li>
                  <Link href={`/${locale}/services`} className="text-sm text-neutral-600 hover:text-neutral-900">
                    {t('footer.miniProgramDevelopment', locale)}
                  </Link>
                </li>
                <li>
                  <Link href={`/${locale}/services`} className="text-sm text-neutral-600 hover:text-neutral-900">
                    {t('footer.backendDevelopment', locale)}
                  </Link>
                </li>
                <li>
                  <Link href={`/${locale}/services`} className="text-sm text-neutral-600 hover:text-neutral-900">
                    {t('footer.globalPlatformDevelopment', locale)}
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-sm font-medium text-neutral-900">
                {t('footer.contact', locale)}
              </h3>
              <ul className="mt-4 space-y-2">
                <li>
                  <a href="mailto:<EMAIL>" className="text-sm text-neutral-600 hover:text-neutral-900">
                    <EMAIL>
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
        {/* 修改 - 底部发丝分隔线与间距维持一致性 [Apple HIG] */}
        <div className="mt-8 border-t border-neutral-200/70 pt-8 md:flex md:items-center md:justify-between">
          <p className="mt-8 text-base text-neutral-500 md:mt-0 md:order-1">
            &copy; {currentYear} {locale === 'zh' ? '三娃软件开发工作室' : 'Sanva Software Development Studio'}. {t('footer.allRightsReserved', locale)}.
            {locale === 'zh' && (
              <>
                {' '}
                <a href="https://beian.miit.gov.cn/" target="_blank" rel="noopener noreferrer" className="hover:text-neutral-700">
                  闽ICP备2024074836号-2
                </a>
              </>
            )}
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer; 