{"common": {"contactUs": "문의하기", "services": "서비스", "about": "회사소개", "home": "홈", "learnMore": "더 알아보기", "loading": "로딩 중...", "submit": "제출", "submitting": "제출 중..."}, "navigation": {"home": "홈", "services": "서비스", "about": "회사소개", "portfolio": "포트폴리오", "blog": "블로그", "team": "팀", "caseStudies": "사례 연구", "pricing": "가격", "contact": "문의하기"}, "home": {"hero": {"title": "효율적이고 우아한 디지털 솔루션 구축", "subtitle": "Sanva 소프트웨어 개발 스튜디오는 기업과 개인을 위한 전문적인 앱 개발, 미니 프로그램 개발, 백엔드 개발 서비스 제공을 전문으로 합니다.", "cta": "문의하기"}, "services": {"title": "우리의 서비스", "subtitle": "모든 디지털 요구사항을 충족하는 포괄적인 소프트웨어 개발 솔루션", "appDev": {"title": "앱 개발", "description": "React Native, Flutter 및 기타 기술 스택을 사용한 네이티브 iOS/Android 또는 크로스 플랫폼 앱 개발"}, "miniProgram": {"title": "미니 프로그램 개발", "description": "WeChat 미니 프로그램, Alipay 미니 프로그램, TikTok 미니 프로그램 등 빠른 배포와 우수한 사용자 경험 제공"}, "backend": {"title": "백엔드 개발", "description": "RESTful API, 데이터베이스 설계, 클라우드 서비스(AWS, Alibaba Cloud 등) 솔루션"}, "webDev": {"title": "웹 개발", "description": "반응형 웹사이트, 기업 웹사이트, 멀티 디바이스 접근을 지원하는 전자상거래 플랫폼"}, "globalPlatforms": {"title": "글로벌 플랫폼 개발", "description": "Google Play 앱, App Store 앱, Facebook 미니게임, WhatsApp Business 및 기타 글로벌 플랫폼 개발"}}, "testimonials": {"title": "고객 후기", "subtitle": "고객들이 우리 서비스에 대해 말하는 것"}, "quickNav": {"title": "우리에 대해 더 알아보기"}}, "services": {"title": "우리의 서비스", "subtitle": "앱 개발, 미니 프로그램 개발, 백엔드 개발을 포함한 전문 소프트웨어 개발 서비스를 제공합니다", "sectionTitle": "포괄적인 소프트웨어 개발 솔루션", "sectionSubtitle": "모바일 앱, 미니 프로그램 또는 백엔드 시스템이 필요하든, 고품질 개발 서비스를 제공하여 비즈니스의 디지털 전환을 지원합니다.", "appDevelopment": {"title": "앱 개발", "description": "최신 기술을 사용한 네이티브 iOS 및 Android 앱 개발 서비스와 크로스 플랫폼 앱 개발 서비스를 제공합니다.", "features": {"native": "네이티브 앱 개발 (iOS/Android)", "crossPlatform": "크로스 플랫폼 앱 개발", "uiUx": "UI/UX 디자인", "maintenance": "앱 유지보수 및 업데이트", "storeSupport": "앱 스토어 등록 지원"}}, "miniProgram": {"title": "미니 프로그램 개발", "description": "WeChat, Alipay, TikTok 미니 프로그램 등 다양한 미니 프로그램 개발에 집중하여 기업이 신속하게 사용자에게 도달할 수 있도록 지원합니다.", "features": {"wechat": "WeChat 미니 프로그램 개발", "alipay": "Alipay 미니 프로그램 개발", "douyin": "TikTok 미니 프로그램 개발", "multiPlatform": "멀티 플랫폼 미니 프로그램 솔루션", "ecommerce": "미니 프로그램 쇼핑몰 및 결제 통합"}}, "backend": {"title": "백엔드 개발", "description": "애플리케이션에 강력한 데이터 지원을 제공하는 신뢰성 높고 안전하며 고성능의 백엔드 개발 서비스를 제공합니다.", "features": {"api": "RESTful API 개발", "database": "데이터베이스 설계 및 최적화", "auth": "사용자 인증 및 권한 부여", "cloud": "클라우드 서비스 통합 (AWS, Alibaba Cloud 등)", "server": "서버 구성 및 유지보수"}}, "globalPlatforms": {"title": "글로벌 플랫폼 개발", "description": "전 세계 사용자에게 제품을 전달할 수 있도록 글로벌 플랫폼용 개발 서비스를 제공합니다.", "features": {"googlePlay": "Google Play 앱 개발", "appStore": "App Store 앱 개발", "facebook": "Facebook 미니게임 개발", "whatsapp": "WhatsApp Business 통합", "telegram": "Telegram 봇 개발", "instagram": "Instagram API 통합", "twitter": "Twitter/X API 통합", "linkedin": "LinkedIn 앱 개발"}}, "process": {"title": "우리의 개발 프로세스", "subtitle": "투명하고 효율적인 개발 프로세스로 원활한 프로젝트 진행과 적시 납품 보장", "steps": {"analysis": {"title": "요구사항 분석", "description": "고객의 비즈니스 요구사항을 깊이 이해하고 프로젝트 목표와 기능 범위를 정의"}, "design": {"title": "설계 및 기획", "description": "기술 솔루션과 디자인 프로토타입을 작성하여 최적의 사용자 경험 보장"}, "development": {"title": "개발 구현", "description": "설계 계획에 따라 개발을 진행하고 정기적으로 진행 상황과 결과를 보고"}, "delivery": {"title": "테스트 및 납품", "description": "애플리케이션 기능을 포괄적으로 테스트하고 품질 확보 후 배포 및 납품 진행"}}}}, "contact": {"title": "문의하기", "subtitle": "고객님의 요구사항을 듣고 언제든지 전문적인 지원을 제공하기를 기대합니다", "methods": {"title": "연락처 정보", "email": "이메일", "workTime": "근무시간", "workHours": "월요일부터 금요일 9:00 - 18:00"}, "followUs": "팔로우하기", "form": {"title": "메시지 보내기", "subtitle": "고객님의 메시지를 기다립니다. 전문 팀이 맞춤형 솔루션을 제공해드립니다", "name": "이름", "email": "이메일", "phone": "전화번호 (선택사항)", "message": "요구사항 설명", "required": "*", "send": "💌 지금 보내기"}}, "about": {"title": "회사소개", "subtitle": "Sanva 소프트웨어 개발 스튜디오의 배경과 전문 역량에 대해 알아보세요", "introduction": {"title": "스튜디오 소개", "paragraphs": {"first": "Sanva 소프트웨어 개발 스튜디오는 2023년에 설립되어 기업과 개인에게 고품질 소프트웨어 솔루션을 제공하는 것을 전문으로 하는 팀입니다. 저희 서비스 범위는 모바일 앱 개발, 미니 프로그램 개발, 백엔드 시스템 개발을 포함합니다.", "second": "저희 팀은 경험 풍부한 개발 엔지니어와 디자이너로 구성되어 있으며, 각자가 탄탄한 기술력과 혁신적인 사고를 가지고 있습니다. 저희는 최첨단 기술 개발에 중점을 두고, 지속적으로 새로운 기술을 학습하고 적용하여 고객에게 최고 품질의 서비스를 제공합니다.", "third": "Sanva 소프트웨어에서는 기술이 사람을 위해 봉사하고 기업에 가치를 창조해야 한다고 믿습니다. 저희는 기술로 실제 문제를 해결하고, 고객의 디지털 전환을 돕고, 운영 효율성을 향상시키며, 시장 경쟁력을 강화하는 데 전념하고 있습니다."}}, "values": {"title": "우리의 핵심 가치", "subtitle": "이러한 가치들이 우리의 일상 업무를 지도하고 고객에게 최고 품질의 서비스를 제공하는 데 도움이 됩니다", "items": {"professional": {"title": "전문성", "description": "저희는 풍부한 기술 경험과 업계 지식을 가지고 있어 고객에게 전문적인 소프트웨어 솔루션을 제공합니다."}, "efficient": {"title": "효율성", "description": "저희는 개발 효율성과 프로젝트 진행 관리에 중점을 두어 고품질 제품의 적시 납품을 보장합니다."}, "customerFirst": {"title": "고객 우선", "description": "저희는 고객의 니즈를 중심에 두고, 개인화된 솔루션과 세심한 애프터서비스를 제공합니다."}}}, "team": {"title": "우리의 팀", "subtitle": "경험 풍부한 전문가들로 구성되어 최고 품질의 서비스를 제공하는 데 전념하고 있습니다", "members": {"founder": {"name": "장 철수", "role": "창립자 / 수석 개발자", "description": "10년의 소프트웨어 개발 경험을 가지고 있으며, 모바일 앱과 미니 프로그램 개발을 전문으로 합니다."}, "designer": {"name": "이 영희", "role": "UI/UX 디자이너", "description": "아름답고 사용자 친화적인 인터페이스 제작에 능숙하며, 사용자 경험의 모든 세부사항에 집중합니다."}, "backend": {"name": "박 민수", "role": "백엔드 개발자", "description": "클라우드 서비스와 데이터베이스 설계의 전문가로, 안정적이고 효율적인 백엔드 시스템을 구축합니다."}}}}, "portfolio": {"viewDetails": "자세히 보기", "coreFeatures": "핵심 기능:"}}