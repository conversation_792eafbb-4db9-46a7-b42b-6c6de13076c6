{"common": {"contactUs": "<PERSON><PERSON>", "services": "Services", "about": "À Propos", "home": "Accueil", "learnMore": "En Savoir Plus", "loading": "Chargement...", "submit": "Envoyer", "submitting": "Envoi en cours...", "language": "<PERSON><PERSON>", "openMainMenu": "<PERSON><PERSON><PERSON><PERSON><PERSON> le menu principal", "closeMainMenu": "<PERSON><PERSON><PERSON> le menu principal"}, "navigation": {"home": "Accueil", "services": "Services", "about": "À Propos", "portfolio": "Portfolio", "blog": "Blog", "team": "Équipe", "caseStudies": "Études de Cas", "pricing": "Tarification", "contact": "Contact"}, "home": {"hero": {"title": "Créer des Solutions Numériques Efficaces et Élégantes", "subtitle": "Sanva Software Development Studio se spécialise dans la fourniture de services professionnels de développement d'applications, de mini-programmes et de backend pour les entreprises et les particuliers.", "cta": "<PERSON><PERSON>"}, "services": {"title": "Nos Services", "subtitle": "Solutions complètes de développement logiciel pour répondre à tous vos besoins numériques", "appDev": {"title": "Développement d'Applications", "description": "Développement d'applications natives iOS/Android ou multiplateformes utilisant React Native, Flutter et autres technologies"}, "miniProgram": {"title": "Développement de Mini-Programmes", "description": "Mini-programmes WeChat, Alipay, TikTok et plus avec déploiement rapide et excellente expérience utilisateur"}, "backend": {"title": "Développement Backend", "description": "APIs RESTful, conception de bases de données, services cloud (AWS, Alibaba Cloud, etc.)"}, "webDev": {"title": "Développement Web", "description": "Sites web responsives, sites d'entreprise, plateformes e-commerce supportant l'accès multi-appareils"}, "globalPlatforms": {"title": "Développement Plateformes Globales", "description": "Applications Google Play, App Store, mini-jeux Facebook, WhatsApp Business et autres plateformes mondiales"}}, "testimonials": {"title": "Témoignages Clients", "subtitle": "Ce que nos clients disent de nos services"}, "quickNav": {"title": "En Savoir Plus Sur Nous"}}, "services": {"title": "Nos Services", "subtitle": "Nous fournissons des services professionnels de développement logiciel incluant le développement d'applications, de mini-programmes et de backend", "sectionTitle": "Solutions Complètes de Développement Logiciel", "sectionSubtitle": "Que vous ayez besoin d'applications mobiles, de mini-programmes ou de systèmes backend, nous fournissons des services de développement de qualité pour aider votre entreprise à réaliser sa transformation numérique.", "appDevelopment": {"title": "Développement d'Applications", "description": "Nous fournissons des services de développement d'applications natives iOS et Android, ainsi que le développement d'applications multiplateformes utilisant des technologies modernes.", "features": {"native": "Développement d'Applications Natives (iOS/Android)", "crossPlatform": "Développement d'Applications Multiplateformes", "uiUx": "Conception UI/UX", "maintenance": "Maintenance et Mises à Jour d'Applications", "storeSupport": "Support de Soumission App Store"}}, "miniProgram": {"title": "Développement de Mini-Programmes", "description": "Nous nous concentrons sur le développement de divers mini-programmes incluant WeChat, Alipay et TikTok pour aider les entreprises à atteindre rapidement les utilisateurs.", "features": {"wechat": "Développement Mini-Programme WeChat", "alipay": "Développement Mini-Programme Alipay", "douyin": "Développement Mini-Programme TikTok", "multiPlatform": "Solutions Mini-Programmes Multiplateformes", "ecommerce": "E-commerce Mini-Programme et Intégration Paiement"}}, "backend": {"title": "Développement Backend", "description": "Nous fournissons des services de développement backend fiables, sécurisés et haute performance pour alimenter vos applications.", "features": {"api": "Développement API RESTful", "database": "Conception et Optimisation de Base de Données", "auth": "Authentification et Autorisation Utilisateur", "cloud": "Intégration Services Cloud (AWS, Alibaba Cloud, etc.)", "server": "Configuration et Maintenance Serveur"}}, "globalPlatforms": {"title": "Développement Plateformes Globales", "description": "Nous fournissons des services de développement pour plateformes globales pour aider vos produits à atteindre les utilisateurs du monde entier.", "features": {"googlePlay": "Développement Application Google Play", "appStore": "Développement Application App Store", "facebook": "Développement Mini-Jeu Facebook", "whatsapp": "Intégration WhatsApp Business", "telegram": "Développement Bot Telegram", "instagram": "Intégration API Instagram", "twitter": "Intégration API Twitter/X", "linkedin": "Développement Application LinkedIn"}}, "process": {"title": "Notre Processus de Développement", "subtitle": "Processus de développement transparent et efficace garantissant un progrès de projet fluide et une livraison ponctuelle", "steps": {"analysis": {"title": "Analyse des Exigences", "description": "Compréhension approfondie de vos besoins commerciaux, définition des objectifs de projet et portée des fonctionnalités"}, "design": {"title": "Conception et Planification", "description": "Création de solutions techniques et prototypes de conception pour assurer une expérience utilisateur optimale"}, "development": {"title": "Implémentation du Développement", "description": "Développement selon les plans de conception avec rapports de progrès réguliers et livrables"}, "delivery": {"title": "Test et Livraison", "description": "Test complet des fonctionnalités d'application, assurance qualité avant déploiement et livraison"}}}}, "contact": {"title": "<PERSON><PERSON>", "subtitle": "Nous avons hâte d'entendre vos besoins et de fournir un support professionnel à tout moment", "methods": {"title": "Informations de Contact", "email": "Email", "workTime": "Heures d'Ouverture", "workHours": "Lundi au Vendredi 9:00 - 18:00"}, "followUs": "Suivez-Nous", "form": {"title": "Envoyer un Message", "subtitle": "Nous attendons votre message. Notre équipe professionnelle vous fournira des solutions personnalisées", "name": "Nom", "email": "Email", "phone": "Téléphone (Optionnel)", "message": "Description des Exigences", "required": "*", "send": "💌 Envoyer Maintenant", "success": {"title": "🎉 Merci pour votre message !", "message": "Nous avons reçu votre message. Notre équipe professionnelle vous contactera dans les 24 heures.", "urgent": "Pour des besoins urgents, veuil<PERSON>z envoyer un email directement à <EMAIL>"}, "errors": {"nameRequired": "Veuillez entrer votre nom", "emailRequired": "Veuillez entrer votre email", "emailInvalid": "Veu<PERSON>z entrer une adresse email valide", "messageRequired": "Veuillez entrer la description de vos exigences"}}, "faq": {"title": "Questions Fréquemment Po<PERSON>ées", "subtitle": "Voici quelques questions fréquemment posées par nos clients. Si vous avez d'autres questions, n'hésitez pas à nous contacter", "questions": {"timeline": {"q": "Combien de temps prend généralement le développement de projet ?", "a": "La chronologie de développement de projet dépend de l'échelle et de la complexité du projet. Les mini-programmes simples peuvent prendre 2-4 semaines, tandis que les applications complexes peuvent nécessiter 2-3 mois. Nous fournissons des estimations de temps détaillées avant le début du projet."}, "pricing": {"q": "Quels sont vos standards de tarification ?", "a": "Nous déterminons les prix basés sur la complexité du projet, le nombre de fonctionnalités et le temps de développement. Nous fournissons des services gratuits d'évaluation de projet et de devis. Veuillez nous contacter pour des informations détaillées."}, "maintenance": {"q": "Fournissez-vous des services de maintenance après le développement ?", "a": "<PERSON><PERSON>, nous fournissons du support technique et des services de maintenance après la livraison du projet. Nous fournissons généralement 1-3 mois de période de maintenance gratuite, suivie de contrats de maintenance à long terme."}, "modification": {"q": "Pouvez-vous modifier des applications ou mini-programmes existants ?", "a": "O<PERSON>. Nous pouvons reprendre et modifier des projets existants, optimiser les fonctionnalités ou corriger les problèmes. Nous évaluons d'abord le code, puis fournissons des plans de modification et des devis."}}}}, "about": {"title": "À Propos", "subtitle": "Découvrez l'histoire et les capacités professionnelles de Sanva Software Development Studio", "introduction": {"title": "Présentation du Studio", "paragraphs": {"first": "Sanva Software Development Studio a été fondé en 2023, spécialisé dans la fourniture de solutions logicielles de haute qualité pour les entreprises et les particuliers. Nos services couvrent le développement d'applications mobiles, le développement de mini-programmes et le développement de systèmes backend.", "second": "Notre équipe se compose d'ingénieurs de développement et de designers expérimentés, chacun avec des compétences techniques solides et une pensée innovante. Nous nous concentrons sur le développement technologique de pointe, apprenant et appliquant continuellement de nouvelles technologies pour garantir que nous fournissons les services de la plus haute qualité à nos clients.", "third": "Chez Sanva Software, nous croyons que la technologie doit servir les gens et créer de la valeur pour les entreprises. Nous nous engageons à résoudre des problèmes réels avec la technologie, aidant les clients à réaliser la transformation numérique, améliorer l'efficacité opérationnelle et renforcer la compétitivité sur le marché."}}, "values": {"title": "Nos Valeurs Fondamentales", "subtitle": "Ces valeurs guident notre travail quotidien et nous aident à fournir les services de la plus haute qualité à nos clients", "items": {"professional": {"title": "Professionnel", "description": "Nous avons une vaste expérience technique et des connaissances sectorielles pour fournir des solutions logicielles professionnelles à nos clients."}, "efficient": {"title": "Efficace", "description": "Nous nous concentrons sur l'efficacité du développement et la gestion des progrès du projet pour assurer une livraison ponctuelle de produits de haute qualité."}, "customerFirst": {"title": "Client d'Abord", "description": "Nous nous concentrons sur les besoins des clients, fournissant des solutions personnalisées et un service après-vente attentionné."}}}, "team": {"title": "Notre Équipe", "subtitle": "Composée de professionnels expérimentés dédiés à vous fournir les services de la plus haute qualité", "members": {"founder": {"name": "<PERSON>", "role": "Fondateur / Développeur Principal", "description": "Avec 10 ans d'expérience en développement logiciel, spécialisé dans le développement d'applications mobiles et de mini-programmes."}, "designer": {"name": "<PERSON>", "role": "Designer UI/UX", "description": "Compétente dans la création d'interfaces belles et conviviales, se concentrant sur chaque détail de l'expérience utilisateur."}, "backend": {"name": "<PERSON>", "role": "Développeur Backend", "description": "Expert en services cloud et conception de bases de données, construisant des systèmes backend stables et efficaces."}}}}, "portfolio": {"viewDetails": "Voir les détails", "coreFeatures": "Fonctionnalités principales :"}}